# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

CAMEL (Communicative Agents for AI Society Study) is a framework for building and studying multi-agent systems. The core concept is to enable communication and collaboration between AI agents to accomplish complex tasks.

### Key Components

1. **Agents**: The fundamental building blocks that can interact with users or other agents
   - `ChatAgent`: Core conversational agent with tool calling capabilities
   - `RolePlaying`: Multi-agent system where agents take on specific roles
   - `Workforce`: Complex multi-agent system for task decomposition and execution

2. **Models**: Support for various LLM backends
   - OpenAI, Anthropic, Mistral, Groq, and many others
   - ModelFactory for easy model instantiation
   - ModelManager for sharing models between agents

3. **Tools**: Extensive toolkit system for agent capabilities
   - Search, code execution, file operations, web browsing
   - Database access, API integrations, specialized domain tools

4. **Memory**: Persistent storage for agent conversations and context
   - Chat history management
   - Vector database integration for long-term memory

## Common Development Commands

### Setup and Installation
```bash
# Install uv if you don't have it already
pip install uv

# Create a virtual environment and install dependencies
uv venv .venv --python=3.10
source .venv/bin/activate  # For macOS/Linux
# or .venv\Scripts\activate  # For Windows

# Install CAMEL with all dependencies
uv pip install -e ".[all, dev, docs]"

# Install pre-commit hooks
pre-commit install
```

### Running Tests
```bash
# Run all tests (requires API keys for some tests)
pytest .

# Run fast local tests only
pytest --fast-test-mode .

# Run tests with coverage
pytest --cov --cov-report=html
```

### Code Quality
```bash
# Run formatting
make format

# Run linting
make ruff

# Run type checking
make mypy

# Run all pre-commit checks
pre-commit run --all-files
```

### Building Documentation
```bash
# Install documentation dependencies
uv pip install -e ".[docs]"

# Build documentation
cd docs && make html
```

## Architecture Overview

### Core Agent Architecture
- BaseAgent defines the interface with `reset()` and `step()` methods
- ChatAgent is the primary implementation with memory, tools, and model integration
- Agents communicate through Message objects that follow a standardized format

### Multi-Agent Systems
- RolePlaying coordinates two agents with specific roles to collaborate
- Workforce manages complex task decomposition with multiple specialized agents
- Agents can share models through ModelManager for efficiency

### Model Integration
- ModelFactory provides a unified interface for instantiating different LLM backends
- BaseModelBackend defines the common interface all models must implement
- Support for both chat completion and structured output APIs

### Tool System
- BaseToolkit defines the interface for tool collections
- FunctionTool wraps Python functions as callable tools for agents
- Tools are automatically converted to the appropriate format for each model backend

### Memory System
- AgentMemory provides interface for storing and retrieving conversation history
- ChatHistoryMemory implements basic conversation storage
- Integration with vector databases for long-term memory and retrieval