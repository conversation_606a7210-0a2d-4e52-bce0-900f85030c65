<a id="camel.toolkits.slack_toolkit"></a>

<a id="camel.toolkits.slack_toolkit.SlackToolkit"></a>

## SlackToolkit

```python
class SlackToolkit(BaseToolkit):
```

A class representing a toolkit for Slack operations.

This class provides methods for Slack operations such as creating a new
channel, joining an existing channel, leaving a channel.

<a id="camel.toolkits.slack_toolkit.SlackToolkit.__init__"></a>

### __init__

```python
def __init__(self, timeout: Optional[float] = None):
```

Initializes a new instance of the SlackToolkit class.

**Parameters:**

- **timeout** (Optional[float]): The timeout value for API requests in seconds. If None, no timeout is applied. (default: :obj:`None`)

<a id="camel.toolkits.slack_toolkit.SlackToolkit._login_slack"></a>

### _login_slack

```python
def _login_slack(
    self,
    slack_token: Optional[str] = None,
    ssl: Optional[SSLContext] = None
):
```

Authenticate using the Slack API.

**Parameters:**

- **slack_token** (str, optional): The Slack API token. If not provided, it attempts to retrieve the token from the environment variable SLACK_BOT_TOKEN or SLACK_USER_TOKEN.
- **ssl** (SSLContext, optional): SSL context for secure connections. Defaults to `None`.

**Returns:**

  WebClient: A WebClient object for interacting with Slack API.

<a id="camel.toolkits.slack_toolkit.SlackToolkit.create_slack_channel"></a>

### create_slack_channel

```python
def create_slack_channel(self, name: str, is_private: Optional[bool] = True):
```

Creates a new slack channel, either public or private.

**Parameters:**

- **name** (str): Name of the public or private channel to create.
- **is_private** (bool, optional): Whether to create a private channel instead of a public one. Defaults to `True`.

**Returns:**

  str: JSON string containing information about Slack
channel created.

<a id="camel.toolkits.slack_toolkit.SlackToolkit.join_slack_channel"></a>

### join_slack_channel

```python
def join_slack_channel(self, channel_id: str):
```

Joins an existing Slack channel. To get the `channel_id` of a
channel, you can use the `get_slack_channel_information` function.

**Parameters:**

- **channel_id** (str): The ID of the Slack channel to join.

**Returns:**

  str: A string containing the API response from Slack.

<a id="camel.toolkits.slack_toolkit.SlackToolkit.leave_slack_channel"></a>

### leave_slack_channel

```python
def leave_slack_channel(self, channel_id: str):
```

Leaves an existing Slack channel. To get the `channel_id` of a
channel, you can use the `get_slack_channel_information` function.

**Parameters:**

- **channel_id** (str): The ID of the Slack channel to leave.

**Returns:**

  str: A string containing the API response from Slack.

<a id="camel.toolkits.slack_toolkit.SlackToolkit.get_slack_channel_information"></a>

### get_slack_channel_information

```python
def get_slack_channel_information(self):
```

**Returns:**

  str: A JSON string representing a list of channels. Each channel
object in the list contains 'id', 'name', 'created', and
'num_members'. Returns an error message string on failure.

<a id="camel.toolkits.slack_toolkit.SlackToolkit.get_slack_channel_message"></a>

### get_slack_channel_message

```python
def get_slack_channel_message(self, channel_id: str):
```

Retrieve messages from a Slack channel. To get the `channel_id`
of a channel, you can use the `get_slack_channel_information`
function.

**Parameters:**

- **channel_id** (str): The ID of the Slack channel to retrieve messages from.

**Returns:**

  str: A JSON string representing a list of messages. Each message
object contains 'user', 'text', and 'ts' (timestamp).

<a id="camel.toolkits.slack_toolkit.SlackToolkit.send_slack_message"></a>

### send_slack_message

```python
def send_slack_message(
    self,
    message: str,
    channel_id: str,
    file_path: Optional[str] = None,
    user: Optional[str] = None
):
```

Send a message to a Slack channel. To get the `channel_id` of a
channel, you can use the `get_slack_channel_information` function.

**Parameters:**

- **message** (str): The message to send.
- **channel_id** (str): The ID of the channel to send the message to.
- **file_path** (Optional[str]): The local path of a file to upload with the message.
- **user** (Optional[str]): The ID of a user to send an ephemeral message to (visible only to that user).

**Returns:**

  str: A confirmation message indicating success or an error
message.

<a id="camel.toolkits.slack_toolkit.SlackToolkit.delete_slack_message"></a>

### delete_slack_message

```python
def delete_slack_message(self, time_stamp: str, channel_id: str):
```

Delete a message from a Slack channel.

**Parameters:**

- **time_stamp** (str): The 'ts' value of the message to be deleted. You can get this from the `get_slack_channel_message` function.
- **channel_id** (str): The ID of the channel where the message is. Use `get_slack_channel_information` to find the `channel_id`.

**Returns:**

  str: A string containing the API response from Slack.

<a id="camel.toolkits.slack_toolkit.SlackToolkit.get_tools"></a>

### get_tools

```python
def get_tools(self):
```

**Returns:**

  List[FunctionTool]: A list of FunctionTool objects
representing the functions in the toolkit.
